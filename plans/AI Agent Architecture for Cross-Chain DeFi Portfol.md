Alright, <PERSON> — time to craft some wicked UI screen prompts for a mobile-only, retro-styled DeFi portfolio AI agent. Think bold, vibrant colors that hit like a synthwave sunrise, with a UI straight out of an arcade fever dream but built for precision-tuned portfolio domination.

Here are tailored UI screen prompts to hand off to a designer or feed into a gen model:

---

### 🟣 1. **Wallet Connection Screen**

**Prompt**:
“Design a mobile wallet connection screen in a retro-futuristic style. Use neon gradients (magenta, electric blue, lime), scanline textures, and pixel-style icons. Large glowing ‘Connect Wallet’ button. Offer options like MetaMask, WalletConnect, Coinbase. Background: glitchy 80s grid landscape fading into a dark blockchain skyline.”

---

### 🔵 2. **Onboarding & Risk Profile Quiz**

**Prompt**:
“Create a multi-step onboarding quiz UI that looks like an 80s arcade game intro. Use pixel fonts, chunky sliders, and bright neon UI components. Risk levels (Conservative, Balanced, Aggressive) should animate like arcade character selects. Retro synth soundtrack visualizer in background.”

---

### 🟢 3. **AI Portfolio Dashboard**

**Prompt**:
“Design a real-time DeFi portfolio dashboard for mobile with heavy 80s influences: neon wireframes, vector-style charts, glowing stat cards. Show total value, chain allocations, protocol breakdown. Animated elements: pulsing yield icons, rotating chain logos (Polygon, Base). Include a ‘Rebalance’ button styled like a joystick.”

---

### 🔴 4. **Protocol Selection & Yield Overview**

**Prompt**:
“Mobile screen showcasing a list of DeFi protocols with neon-outlined cards. Each card displays APY, TVL, and risk score with retro sci-fi meters. Use bright color-coded indicators (green = safe, red = risky). Background: CRT noise with animated vector stars.”

---

### 🟡 5. **Rebalancing Confirmation Modal**

**Prompt**:
“Design a confirmation modal for rebalancing a portfolio. Retro-tech warning frame (think old-school BIOS screen), with blinking cursor animation. Show projected yield changes and gas costs. Big red ‘Confirm’ button styled like an emergency override. Include pulsing caution lines around edges.”

---

### 🟠 6. **Push Notification Settings**

**Prompt**:
“Mobile UI for configuring alerts. Use analog toggle switches with glowing outlines. Alerts like ‘Rebalance Complete’, ‘Yield Spikes’, and ‘Gas Surge’. Visual theme: retro control panel with synthwave toggle sounds and brushed metal UI elements.”

---

### 🟣 7. **Transaction History**

**Prompt**:
“Build a mobile transaction history screen like a digital arcade leaderboard. Neon columns for Date, Protocol, Action, Amount, and Gas Used. Use 8-bit icons for Deposit/Withdraw/Rebalance. Infinite scroll with CRT scroll effects.”

---

### ⚫ 8. **AI Insights Feed**

**Prompt**:
“Design a mobile feed that looks like a retro stock ticker from Blade Runner. Floating AI recommendations and market predictions in neon hologram style. Dark mode only. Tap-to-expand cards open into retro HUD-style overlays.”

---

Let me know if you want prompts adapted for text-to-image tools like Midjourney or DALL·E, or if you want to riff into dark mode or skeuomorphic territory.
