<script setup lang="ts">
interface ChartData {
  label: string;
  value: number;
  color: string;
  percentage: number;
}

interface Props {
  data: ChartData[];
  title: string;
  type?: 'bar' | 'pie' | 'line';
  height?: string;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'bar',
  height: 'h-64',
});

// Calculate max value for bar chart scaling
const maxValue = computed(() => Math.max(...props.data.map((d) => d.value)));
</script>

<template>
  <div class="border-brutal bg-brutal-white shadow-brutal hover-brutal-electric mobile-tap">
    <div class="border-brutal-heavy-bottom bg-brutal-black px-4 py-3 sm:px-6">
      <h3 class="font-brutal mobile-text text-brutal-white uppercase">{{ title }}</h3>
    </div>
    
    <div class="mobile-padding">
      <!-- Bar Chart -->
      <div v-if="type === 'bar'" class="space-y-4" :class="height">
        <div
          v-for="(item, index) in data"
          :key="item.label"
          class="flex items-center space-x-4"
        >
          <div class="w-20 sm:w-24 font-mono-brutal text-xs sm:text-sm text-brutal-black">
            {{ item.label }}
          </div>
          <div class="flex-1 relative">
            <div class="h-6 sm:h-8 border-brutal bg-brutal-charcoal">
              <div
                class="h-full border-brutal transition-all duration-300"
                :class="item.color"
                :style="{ width: `${(item.value / maxValue) * 100}%` }"
              ></div>
            </div>
            <div class="absolute right-2 top-1/2 transform -translate-y-1/2 font-mono-brutal text-xs text-brutal-black">
              {{ item.percentage }}%
            </div>
          </div>
          <div class="w-16 sm:w-20 text-right font-brutal text-xs sm:text-sm text-brutal-black">
            ${{ item.value.toLocaleString() }}
          </div>
        </div>
      </div>

      <!-- Pie Chart (Simple representation) -->
      <div v-else-if="type === 'pie'" class="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6" :class="height">
        <!-- Visual representation -->
        <div class="w-32 h-32 sm:w-40 sm:h-40 relative border-brutal bg-brutal-charcoal">
          <div
            v-for="(item, index) in data"
            :key="item.label"
            class="absolute border-brutal"
            :class="item.color"
            :style="{
              width: `${Math.sqrt(item.percentage)}%`,
              height: `${Math.sqrt(item.percentage)}%`,
              top: `${index * 25}%`,
              left: `${index * 25}%`
            }"
          ></div>
        </div>
        
        <!-- Legend -->
        <div class="space-y-2 flex-1">
          <div
            v-for="item in data"
            :key="item.label"
            class="flex items-center justify-between border-brutal bg-brutal-charcoal px-3 py-2"
          >
            <div class="flex items-center space-x-3">
              <div class="w-4 h-4 border-brutal" :class="item.color"></div>
              <span class="font-mono-brutal text-xs sm:text-sm text-brutal-white">{{ item.label }}</span>
            </div>
            <div class="font-brutal text-xs sm:text-sm text-brutal-white">{{ item.percentage }}%</div>
          </div>
        </div>
      </div>

      <!-- Line Chart (Simple representation) -->
      <div v-else class="space-y-4" :class="height">
        <div class="h-32 sm:h-40 border-brutal bg-brutal-charcoal relative overflow-hidden">
          <!-- Grid lines -->
          <div class="absolute inset-0 grid grid-cols-4 grid-rows-4">
            <div
              v-for="i in 16"
              :key="i"
              class="border-brutal-charcoal border-r border-b opacity-30"
            ></div>
          </div>
          
          <!-- Data points -->
          <div class="absolute inset-0 flex items-end justify-around px-2">
            <div
              v-for="(item, index) in data"
              :key="item.label"
              class="flex flex-col items-center space-y-1"
            >
              <div
                class="w-2 sm:w-3 border-brutal transition-all duration-300"
                :class="item.color"
                :style="{ height: `${(item.value / maxValue) * 80}%` }"
              ></div>
              <div class="font-mono-brutal text-xs text-brutal-white transform rotate-45 origin-bottom-left">
                {{ item.label }}
              </div>
            </div>
          </div>
        </div>
        
        <!-- Values -->
        <div class="grid grid-cols-2 sm:grid-cols-4 gap-2">
          <div
            v-for="item in data"
            :key="item.label"
            class="border-brutal bg-brutal-charcoal px-2 py-1 text-center"
          >
            <div class="font-mono-brutal text-xs text-brutal-white">{{ item.label }}</div>
            <div class="font-brutal text-sm text-brutal-white">${{ item.value.toLocaleString() }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
