<script setup lang="ts">
import type { HTMLAttributes } from 'vue';
import { cn } from '@/lib/utils';

const props = defineProps<{
  class?: HTMLAttributes['class'];
}>();
</script>

<template>
  <div
    data-slot="card-header"
    :class="cn('@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-brutal px-brutal-xl has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-brutal', props.class)"
  >
    <slot />
  </div>
</template>
