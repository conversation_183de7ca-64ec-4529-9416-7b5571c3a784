import { cva, type VariantProps } from 'class-variance-authority';

export { default as Badge } from './Badge.vue';

export const badgeVariants = cva(
  'inline-flex items-center justify-center border-brutal px-2 py-0.5 text-xs font-brutal w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none shadow-brutal transition-all overflow-hidden',
  {
    variants: {
      variant: {
        default: 'bg-neon-lime text-brutal-black [a&]:hover:bg-neon-lime/90',
        secondary: 'bg-neon-cyan text-brutal-black [a&]:hover:bg-neon-cyan/90',
        destructive:
          'bg-neon-pink text-brutal-white [a&]:hover:bg-neon-pink/90',
        outline:
          'bg-brutal-white text-brutal-black [a&]:hover:bg-brutal-charcoal [a&]:hover:text-brutal-white',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);
export type BadgeVariants = VariantProps<typeof badgeVariants>;
