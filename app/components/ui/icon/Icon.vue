<template>
  <component :is="iconComponent" v-bind="$attrs" />
</template>

<script setup lang="ts">
import * as LucideIcons from 'lucide-vue-next';
import { computed } from 'vue';

interface Props {
  name: string;
}

const props = defineProps<Props>();

const iconComponent = computed(() => {
  // Convert kebab-case to PascalCase
  const iconName = props.name
    .split(':')[1] // Remove 'lucide:' prefix
    .split('-')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');

  // biome-ignore lint/suspicious/noExplicitAny: <there is no other option>
  return (LucideIcons as any)[iconName] || LucideIcons.HelpCircle;
});
</script>
