<template>
  <div class="wallet-connect">
    <!-- Connected State -->
    <div v-if="isConnected && address" class="flex items-center gap-brutal-sm">
      <div class="hidden sm:flex items-center gap-brutal-sm">
        <div class="border-brutal bg-brutal-white px-3 py-2 font-mono-brutal text-xs text-brutal-black">
          {{ formatAddress(address) }}
        </div>
        <div v-if="balance" class="border-brutal bg-neon-lime px-3 py-2 font-mono-brutal text-xs text-brutal-black">
          {{ formatBalance(balance.value, balance.decimals) }} ETH
        </div>
      </div>

      <!-- Authenticated State -->
      <div v-if="isAuthenticated" class="flex items-center gap-brutal-sm">
        <div class="border-brutal bg-acid-green px-2 py-1 font-mono-brutal text-xs text-brutal-black">
          ✓ AUTHENTICATED
        </div>
        <Button
          class="border-brutal bg-neon-pink px-4 py-2 font-brutal text-xs uppercase text-brutal-white shadow-brutal hover-brutal-electric mobile-tap"
          @click="handleLogout"
        >
          LOGOUT
        </Button>
      </div>

      <!-- Connected but Not Authenticated -->
      <div v-else class="flex items-center gap-brutal-sm">
        <div class="border-brutal bg-neon-orange px-2 py-1 font-mono-brutal text-xs text-brutal-black">
          ⚠ SIGN IN REQUIRED
        </div>
        <Button
          class="border-brutal bg-electric-blue px-4 py-2 font-brutal text-xs uppercase text-brutal-white shadow-brutal hover-brutal-neon mobile-tap"
          @click="handleSignIn"
          :disabled="isSigningIn"
        >
          {{ isSigningIn ? 'SIGNING...' : 'SIGN IN' }}
        </Button>
        <Button
          class="border-brutal bg-brutal-charcoal px-4 py-2 font-brutal text-xs uppercase text-brutal-white shadow-brutal hover-brutal mobile-tap"
          @click="disconnect"
        >
          DISCONNECT
        </Button>
      </div>
    </div>

    <!-- Disconnected State -->
    <div v-else>
      <Button
        class="border-brutal-thick bg-hot-magenta px-4 py-2 sm:px-6 sm:py-3 font-brutal text-xs sm:text-sm uppercase text-brutal-white shadow-brutal hover-brutal-electric mobile-tap"
        @click="openConnectModal"
      >
        <span class="hidden sm:inline">CONNECT </span>WALLET
      </Button>
    </div>

    <!-- Connect Modal -->
    <Sheet v-model:open="showConnectModal">
      <SheetContent side="right" class="w-80 bg-brutal-black border-brutal-heavy">
        <SheetHeader>
          <SheetTitle class="font-brutal text-2xl text-neon-lime">CONNECT WALLET</SheetTitle>
        </SheetHeader>

        <div class="mt-8 space-y-4">
          <div v-if="connectError" class="border-brutal bg-neon-pink px-4 py-3 font-mono-brutal text-sm text-brutal-white">
            {{ connectError.message }}
          </div>

          <div v-for="connector in connectors" :key="connector.id" class="space-y-2">
            <Button
              class="w-full border-brutal bg-neon-lime px-6 py-4 font-brutal text-sm uppercase text-brutal-black shadow-brutal hover-brutal-electric mobile-tap"
              @click="connectWithConnector(connector)"
              :disabled="isConnecting"
            >
              {{ isConnecting ? 'CONNECTING...' : connector.name }}
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  </div>
</template>

<script setup lang="ts">
import { useAccount, useBalance, useConnect, useDisconnect } from '@wagmi/vue';
import { formatUnits } from 'viem';
import { ref, watch } from 'vue';
import { useAuth } from '@/composables/useAuth';

// Wagmi composables
const { address, isConnected } = useAccount();
const {
  connectors,
  connect,
  error: connectError,
  isPending: isConnecting,
} = useConnect();
const { disconnect } = useDisconnect();
const { data: balance } = useBalance({ address });

// Authentication composable
const { isAuthenticated, signIn, logout, isSigningIn } = useAuth();

// Modal state
const showConnectModal = ref(false);

// Methods
const openConnectModal = () => {
  showConnectModal.value = true;
};

// biome-ignore lint/suspicious/noExplicitAny: Wagmi connector type is complex
const connectWithConnector = async (connector: any) => {
  try {
    connect({ connector });
    showConnectModal.value = false;
  } catch (error) {
    console.error('Connection failed:', error);
  }
};

const handleSignIn = async () => {
  if (!address.value) return;

  try {
    await signIn(address.value);
  } catch (error) {
    console.error('Sign in failed:', error);
  }
};

const handleLogout = async () => {
  await logout();
  disconnect();
};

const formatAddress = (addr: string) => {
  return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
};

const formatBalance = (value: bigint, decimals: number) => {
  return Number.parseFloat(formatUnits(value, decimals)).toFixed(4);
};

// Auto-trigger sign in after successful connection
watch([isConnected, address], ([connected, addr]) => {
  if (connected && addr && !isAuthenticated.value) {
    // Auto-trigger sign in flow after connection
    setTimeout(() => {
      handleSignIn();
    }, 500);
  }
});
</script>