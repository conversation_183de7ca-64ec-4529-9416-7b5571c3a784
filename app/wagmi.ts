import { createConfig, http } from '@wagmi/vue';
import { mainnet } from '@wagmi/vue/chains';
import { coinbaseWallet, injected } from '@wagmi/vue/connectors';

export const config = createConfig({
  chains: [mainnet],
  connectors: [injected(), coinbaseWallet({ appName: 'DEFI.AI' })],
  transports: {
    [mainnet.id]: http(),
  },
});

declare module '@wagmi/vue' {
  interface Register {
    config: typeof config;
  }
}
