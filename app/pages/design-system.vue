<script setup lang="ts">
// SEO and meta
useHead({
  title: 'Design System - DEFI.AI',
  meta: [
    {
      name: 'description',
      content:
        'Neo-Brutalism design system showcase with mobile-responsive components.',
    },
  ],
});

// Sample data for components
const chartData = [
  { label: 'AAVE', value: 1105555, color: 'bg-neon-lime', percentage: 45 },
  { label: 'UNISWAP', value: 859876, color: 'bg-neon-cyan', percentage: 35 },
  { label: 'LIDO', value: 491358, color: 'bg-plasma-orange', percentage: 20 },
];

const colorPalette = [
  { name: 'Electric Blue', class: 'bg-electric-blue', hex: '#0066ff' },
  { name: 'Hot Magenta', class: 'bg-hot-magenta', hex: '#ff006e' },
  { name: 'Acid Green', class: 'bg-acid-green', hex: '#39ff14' },
  { name: 'Laser Red', class: 'bg-laser-red', hex: '#ff073a' },
  { name: 'Cyber Purple', class: 'bg-cyber-purple', hex: '#8a2be2' },
  { name: 'Toxic Yellow', class: 'bg-toxic-yellow', hex: '#ffff00' },
  { name: 'Neon Violet', class: 'bg-neon-violet', hex: '#9d00ff' },
  { name: 'Plasma Orange', class: 'bg-plasma-orange', hex: '#ff4500' },
];

const buttonVariants = [
  {
    label: 'Primary',
    class: 'bg-electric-blue text-brutal-white hover-brutal-electric',
  },
  {
    label: 'Secondary',
    class: 'bg-acid-green text-brutal-black hover-brutal-neon',
  },
  {
    label: 'Danger',
    class: 'bg-laser-red text-brutal-white hover-brutal-magenta',
  },
  {
    label: 'Warning',
    class: 'bg-toxic-yellow text-brutal-black hover-brutal-cyan',
  },
];
</script>

<template>
  <div class="min-h-screen bg-brutal-white">
    <!-- Header -->
    <div class="border-brutal-heavy-bottom bg-brutal-black mobile-padding py-6 sm:py-8">
      <div class="mx-auto max-w-7xl">
        <h1 class="font-brutal mobile-heading text-brutal-white">
          DESIGN
          <span class="text-electric-blue">SYSTEM</span>
        </h1>
        <p class="font-mono-brutal mt-2 mobile-text text-brutal-white">
          > NEO-BRUTALISM MOBILE-RESPONSIVE COMPONENTS
        </p>
      </div>
    </div>

    <div class="mx-auto max-w-7xl mobile-padding py-6 sm:py-8 space-y-8 sm:space-y-12">
      
      <!-- Color Palette Section -->
      <section>
        <h2 class="font-brutal text-2xl sm:text-3xl text-brutal-black mb-6">
          ENHANCED COLOR PALETTE
        </h2>
        <div class="mobile-grid gap-4">
          <div
            v-for="color in colorPalette"
            :key="color.name"
            class="border-brutal bg-brutal-white shadow-brutal hover-brutal-electric mobile-tap"
          >
            <div class="h-16 sm:h-20 border-brutal-heavy-bottom" :class="color.class"></div>
            <div class="mobile-padding">
              <h3 class="font-brutal text-sm sm:text-base text-brutal-black">{{ color.name }}</h3>
              <p class="font-mono-brutal text-xs sm:text-sm text-brutal-black">{{ color.hex }}</p>
            </div>
          </div>
        </div>
      </section>

      <!-- Button Variants Section -->
      <section>
        <h2 class="font-brutal text-2xl sm:text-3xl text-brutal-black mb-6">
          BUTTON VARIANTS
        </h2>
        <div class="mobile-stack gap-4">
          <Button
            v-for="variant in buttonVariants"
            :key="variant.label"
            class="border-brutal px-6 py-3 font-brutal mobile-text shadow-brutal mobile-tap touch-target"
            :class="variant.class"
          >
            {{ variant.label }} BUTTON
          </Button>
        </div>
      </section>

      <!-- Cards Section -->
      <section>
        <h2 class="font-brutal text-2xl sm:text-3xl text-brutal-black mb-6">
          RESPONSIVE CARDS
        </h2>
        <div class="mobile-grid gap-6">
          <Card class="border-brutal bg-brutal-white shadow-brutal-electric-blue hover-brutal-electric mobile-tap">
            <CardHeader>
              <CardTitle class="font-brutal mobile-text text-brutal-black">METRIC CARD</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="font-brutal text-3xl sm:text-4xl text-brutal-black">$2.4M</div>
              <div class="font-mono-brutal text-xs sm:text-sm text-electric-blue">+12.3%</div>
            </CardContent>
          </Card>

          <Card class="border-brutal bg-brutal-black shadow-brutal-acid-green hover-brutal-neon mobile-tap">
            <CardHeader>
              <CardTitle class="font-brutal mobile-text text-acid-green">AI INSIGHT</CardTitle>
            </CardHeader>
            <CardContent>
              <p class="font-mono-brutal mobile-text text-brutal-white">
                Rebalance opportunity detected: ****% APY potential
              </p>
            </CardContent>
          </Card>

          <Card class="border-brutal bg-hot-magenta shadow-brutal hover-brutal-magenta mobile-tap">
            <CardHeader>
              <CardTitle class="font-brutal mobile-text text-brutal-white">ALERT CARD</CardTitle>
            </CardHeader>
            <CardContent>
              <p class="font-mono-brutal mobile-text text-brutal-white">
                High volatility detected in portfolio
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      <!-- Chart Component Section -->
      <section>
        <h2 class="font-brutal text-2xl sm:text-3xl text-brutal-black mb-6">
          DATA VISUALIZATION
        </h2>
        <div class="space-y-6">
          <Chart :data="chartData" title="PORTFOLIO ALLOCATION" type="bar" />
          <Chart :data="chartData" title="ASSET DISTRIBUTION" type="pie" />
        </div>
      </section>

      <!-- Typography Section -->
      <section>
        <h2 class="font-brutal text-2xl sm:text-3xl text-brutal-black mb-6">
          RESPONSIVE TYPOGRAPHY
        </h2>
        <div class="space-y-4 border-brutal bg-brutal-charcoal mobile-padding">
          <h1 class="font-brutal mobile-heading text-electric-blue">HEADING 1</h1>
          <h2 class="font-brutal text-2xl sm:text-3xl md:text-4xl text-acid-green">HEADING 2</h2>
          <h3 class="font-brutal text-xl sm:text-2xl md:text-3xl text-hot-magenta">HEADING 3</h3>
          <p class="font-mono-brutal mobile-text text-brutal-white">
            This is body text that scales responsively across different screen sizes.
            It maintains readability while following Neo-Brutalism principles.
          </p>
          <p class="font-mono-brutal text-xs sm:text-sm text-brutal-white">
            Small text for captions and metadata.
          </p>
        </div>
      </section>

      <!-- Mobile Utilities Section -->
      <section>
        <h2 class="font-brutal text-2xl sm:text-3xl text-brutal-black mb-6">
          MOBILE UTILITIES
        </h2>
        <div class="space-y-4">
          <div class="mobile-stack gap-4 border-brutal bg-brutal-charcoal mobile-padding">
            <div class="border-brutal bg-neon-lime px-4 py-2 font-brutal text-sm text-brutal-black">
              MOBILE STACK
            </div>
            <div class="border-brutal bg-neon-cyan px-4 py-2 font-brutal text-sm text-brutal-black">
              RESPONSIVE
            </div>
            <div class="border-brutal bg-plasma-orange px-4 py-2 font-brutal text-sm text-brutal-black">
              LAYOUT
            </div>
          </div>

          <div class="mobile-grid gap-4">
            <div class="border-brutal bg-electric-blue mobile-padding text-center">
              <div class="font-brutal mobile-text text-brutal-white">GRID ITEM 1</div>
            </div>
            <div class="border-brutal bg-hot-magenta mobile-padding text-center">
              <div class="font-brutal mobile-text text-brutal-white">GRID ITEM 2</div>
            </div>
            <div class="border-brutal bg-acid-green mobile-padding text-center">
              <div class="font-brutal mobile-text text-brutal-black">GRID ITEM 3</div>
            </div>
            <div class="border-brutal bg-laser-red mobile-padding text-center">
              <div class="font-brutal mobile-text text-brutal-white">GRID ITEM 4</div>
            </div>
          </div>
        </div>
      </section>

    </div>
  </div>
</template>
