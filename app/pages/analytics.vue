<script setup lang="ts">
// SEO and meta
useHead({
  title: 'Analytics & Performance - DEFI.AI',
  meta: [
    {
      name: 'description',
      content:
        'Comprehensive analytics dashboard with performance metrics, risk analysis, and AI-powered insights.',
    },
  ],
});

// Time period selector
const selectedPeriod = ref('30D');
const timePeriods = ['1D', '7D', '30D', '90D', '1Y', 'ALL'];

// Performance data
const performanceData = ref({
  totalReturn: '+$456,789',
  totalReturnPercent: '+22.84%',
  sharpeRatio: '2.34',
  maxDrawdown: '-8.2%',
  volatility: '12.5%',
  winRate: '78.3%',
});

// Protocol performance
const protocolPerformance = ref([
  {
    name: 'UNISWAP V3',
    apy: '32.1%',
    tvl: '$859,876',
    rank: 1,
    change: '+5.2%',
  },
  { name: 'LIDO', apy: '24.3%', tvl: '$491,358', rank: 2, change: '+2.1%' },
  { name: 'AAVE', apy: '18.5%', tvl: '$1,105,555', rank: 3, change: '+1.8%' },
]);

// Risk metrics
const riskMetrics = ref([
  { metric: 'VALUE AT RISK (95%)', value: '$45,678', status: 'NORMAL' },
  { metric: 'BETA', value: '1.23', status: 'MODERATE' },
  { metric: 'CORRELATION', value: '0.67', status: 'NORMAL' },
  { metric: 'LIQUIDITY RISK', value: 'LOW', status: 'GOOD' },
]);

// Gas optimization savings
const gasOptimization = ref({
  totalSaved: '$2,456',
  avgGasPrice: '45 GWEI',
  optimizationRate: '87%',
  lastOptimization: '2 hours ago',
});

// AI insights
const aiInsights = ref([
  {
    type: 'OPPORTUNITY',
    title: 'MARKET INEFFICIENCY DETECTED',
    description: 'Arbitrage opportunity between AAVE and Compound detected',
    confidence: 94,
    impact: 'HIGH',
  },
  {
    type: 'WARNING',
    title: 'INCREASED VOLATILITY',
    description: 'Market volatility increased by 15% in the last 24h',
    confidence: 89,
    impact: 'MEDIUM',
  },
  {
    type: 'INFO',
    title: 'PROTOCOL UPDATE',
    description: 'Uniswap V3 fee tier optimization available',
    confidence: 76,
    impact: 'LOW',
  },
]);

// Chart data (mock)
const chartData = ref([
  { date: '2024-01-01', value: 100000 },
  { date: '2024-01-15', value: 105000 },
  { date: '2024-02-01', value: 110000 },
  { date: '2024-02-15', value: 108000 },
  { date: '2024-03-01', value: 115000 },
  { date: '2024-03-15', value: 122000 },
]);
</script>

<template>
  <div class="min-h-screen bg-brutal-charcoal">
    <!-- Header -->
    <div class="border-brutal-heavy bg-brutal-black container-spacing section-spacing">
      <div class="mx-auto max-w-7xl">
        <div class="mobile-margin-x">
          <h1 class="font-brutal text-4xl text-brutal-white md:text-6xl">
            ANALYTICS &
            <span class="text-neon-cyan">PERFORMANCE</span>
          </h1>
          <p class="font-mono-brutal mt-brutal text-brutal-white">
            > COMPREHENSIVE PORTFOLIO ANALYTICS DASHBOARD
          </p>
        </div>
      </div>
    </div>

    <div class="mx-auto max-w-7xl container-spacing section-spacing">
      <!-- Time Period Selector -->
      <div class="mb-brutal-2xl">
        <div class="flex flex-wrap gap-2">
          <button
            v-for="period in timePeriods"
            :key="period"
            class="border-brutal px-4 py-2 font-brutal text-sm transition-all hover-brutal"
            :class="{
              'bg-neon-lime text-brutal-black': selectedPeriod === period,
              'bg-brutal-white text-brutal-black': selectedPeriod !== period
            }"
            @click="selectedPeriod = period"
          >
            {{ period }}
          </button>
        </div>
      </div>

      <!-- Performance Overview -->
      <div class="mb-8 grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-6">
        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardContent class="p-4">
            <div class="font-mono-brutal text-xs text-brutal-black">TOTAL RETURN</div>
            <div class="font-brutal text-lg text-brutal-black">{{ performanceData.totalReturn }}</div>
            <div class="font-mono-brutal text-xs text-neon-lime">{{ performanceData.totalReturnPercent }}</div>
          </CardContent>
        </Card>

        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardContent class="p-4">
            <div class="font-mono-brutal text-xs text-brutal-black">SHARPE RATIO</div>
            <div class="font-brutal text-lg text-brutal-black">{{ performanceData.sharpeRatio }}</div>
            <div class="font-mono-brutal text-xs text-neon-lime">EXCELLENT</div>
          </CardContent>
        </Card>

        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardContent class="p-4">
            <div class="font-mono-brutal text-xs text-brutal-black">MAX DRAWDOWN</div>
            <div class="font-brutal text-lg text-brutal-black">{{ performanceData.maxDrawdown }}</div>
            <div class="font-mono-brutal text-xs text-neon-orange">MODERATE</div>
          </CardContent>
        </Card>

        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardContent class="p-4">
            <div class="font-mono-brutal text-xs text-brutal-black">VOLATILITY</div>
            <div class="font-brutal text-lg text-brutal-black">{{ performanceData.volatility }}</div>
            <div class="font-mono-brutal text-xs text-neon-cyan">LOW</div>
          </CardContent>
        </Card>

        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardContent class="p-4">
            <div class="font-mono-brutal text-xs text-brutal-black">WIN RATE</div>
            <div class="font-brutal text-lg text-brutal-black">{{ performanceData.winRate }}</div>
            <div class="font-mono-brutal text-xs text-neon-lime">HIGH</div>
          </CardContent>
        </Card>

        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardContent class="p-4">
            <div class="font-mono-brutal text-xs text-brutal-black">GAS SAVED</div>
            <div class="font-brutal text-lg text-brutal-black">{{ gasOptimization.totalSaved }}</div>
            <div class="font-mono-brutal text-xs text-neon-lime">{{ gasOptimization.optimizationRate }}</div>
          </CardContent>
        </Card>
      </div>

      <!-- Charts Section -->
      <div class="mb-8">
        <Card class="border-brutal bg-brutal-black shadow-brutal-neon-lime">
          <CardHeader>
            <CardTitle class="font-brutal text-neon-lime">PORTFOLIO PERFORMANCE CHART</CardTitle>
          </CardHeader>
          <CardContent>
            <!-- Placeholder for chart -->
            <div class="border-brutal bg-brutal-charcoal p-8 text-center">
              <div class="font-mono-brutal text-brutal-white">
                [PERFORMANCE CHART VISUALIZATION]
                <br />
                Portfolio Value Over Time: {{ selectedPeriod }}
                <br />
                <div class="mt-4 grid grid-cols-6 gap-2">
                  <div
                    v-for="(point, index) in chartData"
                    :key="index"
                    class="bg-neon-lime h-16"
                    :style="{ height: (point.value / 1000) + 'px' }"
                  ></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Protocol Performance & Risk Metrics -->
      <div class="mb-8 grid grid-cols-1 gap-8 lg:grid-cols-2">
        <!-- Protocol Performance -->
        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardHeader>
            <CardTitle class="font-brutal text-brutal-black">PROTOCOL PERFORMANCE</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div
                v-for="protocol in protocolPerformance"
                :key="protocol.name"
                class="border-brutal bg-brutal-charcoal p-4"
              >
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <div class="font-brutal text-2xl text-brutal-white">#{{ protocol.rank }}</div>
                    <div>
                      <div class="font-brutal text-brutal-white">{{ protocol.name }}</div>
                      <div class="font-mono-brutal text-sm text-brutal-white">{{ protocol.tvl }}</div>
                    </div>
                  </div>
                  <div class="text-right">
                    <div class="font-brutal text-brutal-white">{{ protocol.apy }}</div>
                    <div class="font-mono-brutal text-sm text-neon-lime">{{ protocol.change }}</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Risk Metrics -->
        <Card class="border-brutal bg-brutal-white shadow-brutal">
          <CardHeader>
            <CardTitle class="font-brutal text-brutal-black">RISK METRICS</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div
                v-for="risk in riskMetrics"
                :key="risk.metric"
                class="border-brutal bg-brutal-charcoal p-4"
              >
                <div class="flex items-center justify-between">
                  <div>
                    <div class="font-mono-brutal text-sm text-brutal-white">{{ risk.metric }}</div>
                    <div class="font-brutal text-lg text-brutal-white">{{ risk.value }}</div>
                  </div>
                  <div
                    class="font-mono-brutal text-sm"
                    :class="{
                      'text-neon-lime': risk.status === 'GOOD' || risk.status === 'NORMAL',
                      'text-neon-orange': risk.status === 'MODERATE',
                      'text-neon-pink': risk.status === 'HIGH'
                    }"
                  >
                    {{ risk.status }}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- AI Insights -->
      <div class="mb-8">
        <Card class="border-brutal bg-brutal-black shadow-brutal-neon-cyan">
          <CardHeader>
            <CardTitle class="font-brutal text-neon-cyan">AI INSIGHTS & ALERTS</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div
                v-for="insight in aiInsights"
                :key="insight.title"
                class="border-brutal bg-brutal-charcoal p-4"
              >
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2">
                      <div
                        class="h-3 w-3 border-brutal"
                        :class="{
                          'bg-neon-lime': insight.type === 'OPPORTUNITY',
                          'bg-neon-orange': insight.type === 'WARNING',
                          'bg-neon-cyan': insight.type === 'INFO'
                        }"
                      ></div>
                      <div class="font-brutal text-sm text-brutal-white">{{ insight.title }}</div>
                    </div>
                    <div class="font-mono-brutal mt-2 text-xs text-brutal-white">{{ insight.description }}</div>
                  </div>
                  <div class="text-right">
                    <div
                      class="font-mono-brutal text-xs"
                      :class="{
                        'text-neon-pink': insight.impact === 'HIGH',
                        'text-neon-orange': insight.impact === 'MEDIUM',
                        'text-neon-cyan': insight.impact === 'LOW'
                      }"
                    >
                      {{ insight.impact }}
                    </div>
                    <div class="font-mono-brutal text-xs text-brutal-white">{{ insight.confidence }}%</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Export & Actions -->
      <div class="flex flex-wrap gap-4">
        <Button class="border-brutal bg-neon-lime px-6 py-3 font-brutal text-brutal-black shadow-brutal hover-brutal">
          EXPORT REPORT
        </Button>
        <Button class="border-brutal bg-neon-cyan px-6 py-3 font-brutal text-brutal-black shadow-brutal hover-brutal">
          DOWNLOAD CSV
        </Button>
        <Button class="border-brutal bg-neon-orange px-6 py-3 font-brutal text-brutal-black shadow-brutal hover-brutal">
          SHARE ANALYTICS
        </Button>
      </div>
    </div>
  </div>
</template>
