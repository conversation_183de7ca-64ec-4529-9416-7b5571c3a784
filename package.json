{"name": "defi-agent", "private": true, "type": "module", "packageManager": "bun@1.2.13", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "npx nuxthub preview", "deploy": "npx nuxthub deploy", "postinstall": "nuxt prepare && lefthook install", "format": "biome format --write .", "lint": "bunx oxlint@latest --fix --fix-suggestions", "biome:lint": "biome lint .", "biome:check": "biome check --write .", "biome:ci": "biome ci .", "lefthook:install": "lefthook install", "lefthook:uninstall": "lefthook uninstall"}, "dependencies": {"@nuxthub/core": "^0.8.25", "@tailwindcss/vite": "^4.1.7", "@tanstack/vue-query": "^5.77.0", "@vueuse/core": "^13.2.0", "@wagmi/core": "^2.17.2", "@wagmi/vue": "^0.1.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.511.0", "nuxt": "^3.16.2", "reka-ui": "^2.2.1", "shadcn-nuxt": "2.1.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "viem": "^2.30.1", "vite-plugin-oxlint": "^1.3.3", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@biomejs/biome": "2.0.0-beta.5", "@pinia/nuxt": "^0.11.0", "@vueuse/nuxt": "^13.2.0", "lefthook": "^1.11.13", "nuxt-mcp": "^0.2.2", "oxlint": "^0.16.11", "typescript": "^5.8.3", "vue-tsc": "^2.2.10", "wrangler": "^4.16.1"}}